#!/usr/bin/env python3
"""
Analysis script for model evaluation results
"""

import csv
import pandas as pd
from typing import Dict, List

def analyze_results(csv_file: str = "evaluation_results.csv"):
    """Analyze the evaluation results"""
    
    print("=" * 80)
    print("MODEL EVALUATION RESULTS ANALYSIS")
    print("=" * 80)
    
    # Read CSV file
    try:
        df = pd.read_csv(csv_file)
    except FileNotFoundError:
        print(f"Results file {csv_file} not found. Please run the evaluation first.")
        return
    except Exception as e:
        print(f"Error reading results file: {e}")
        return
    
    total_cases = len(df)
    print(f"Total cases evaluated: {total_cases}")
    print()
    
    # Validation analysis
    reasoning_valid = df['gemini_reasoning_valid'].sum()
    close_valid = df['gemini_close_valid'].sum()
    
    print("VALIDATION RESULTS:")
    print("-" * 40)
    print(f"Gemini Reasoning (500 tokens) valid cases: {reasoning_valid}/{total_cases} ({reasoning_valid/total_cases*100:.1f}%)")
    print(f"Gemini Close (0 tokens) valid cases: {close_valid}/{total_cases} ({close_valid/total_cases*100:.1f}%)")
    print()
    
    # Knowledge count analysis
    print("KNOWLEDGE COUNT ANALYSIS:")
    print("-" * 40)
    
    # Ground truth stats
    gt_avg = df['ground_truth_ids'].apply(lambda x: len(x.split(';')) if x else 0).mean()
    gt_min_avg = df['ground_truth_count_min'].mean()
    gt_max_avg = df['ground_truth_count_max'].mean()
    
    print(f"Ground Truth (Claude):")
    print(f"  Average intersection count: {gt_avg:.1f}")
    print(f"  Average min count per call: {gt_min_avg:.1f}")
    print(f"  Average max count per call: {gt_max_avg:.1f}")
    
    # Gemini reasoning stats
    gr_avg = df['gemini_reasoning_ids'].apply(lambda x: len(x.split(';')) if x else 0).mean()
    gr_min_avg = df['gemini_reasoning_count_min'].mean()
    gr_max_avg = df['gemini_reasoning_count_max'].mean()
    
    print(f"Gemini Reasoning:")
    print(f"  Average intersection count: {gr_avg:.1f}")
    print(f"  Average min count per call: {gr_min_avg:.1f}")
    print(f"  Average max count per call: {gr_max_avg:.1f}")
    
    # Gemini close stats
    gc_avg = df['gemini_close_ids'].apply(lambda x: len(x.split(';')) if x else 0).mean()
    gc_min_avg = df['gemini_close_count_min'].mean()
    gc_max_avg = df['gemini_close_count_max'].mean()
    
    print(f"Gemini Close:")
    print(f"  Average intersection count: {gc_avg:.1f}")
    print(f"  Average min count per call: {gc_min_avg:.1f}")
    print(f"  Average max count per call: {gc_max_avg:.1f}")
    print()
    
    # Response time analysis
    print("RESPONSE TIME ANALYSIS:")
    print("-" * 40)
    
    gt_time_avg = (df['ground_truth_time_min'] + df['ground_truth_time_max']).mean() / 2
    gr_time_avg = (df['gemini_reasoning_time_min'] + df['gemini_reasoning_time_max']).mean() / 2
    gc_time_avg = (df['gemini_close_time_min'] + df['gemini_close_time_max']).mean() / 2
    
    print(f"Average response times:")
    print(f"  Ground Truth (Claude): {gt_time_avg:.2f}s")
    print(f"  Gemini Reasoning: {gr_time_avg:.2f}s")
    print(f"  Gemini Close: {gc_time_avg:.2f}s")
    print()
    
    # Detailed case analysis
    print("DETAILED CASE ANALYSIS:")
    print("-" * 40)
    
    for idx, row in df.iterrows():
        case_num = row['case_index']
        print(f"Case {case_num}:")
        
        # Parse knowledge IDs
        gt_ids = set(row['ground_truth_ids'].split(';')) if row['ground_truth_ids'] else set()
        gr_ids = set(row['gemini_reasoning_ids'].split(';')) if row['gemini_reasoning_ids'] else set()
        gc_ids = set(row['gemini_close_ids'].split(';')) if row['gemini_close_ids'] else set()
        
        print(f"  Ground Truth IDs ({len(gt_ids)}): {sorted(gt_ids)}")
        print(f"  Gemini Reasoning IDs ({len(gr_ids)}): {sorted(gr_ids)}")
        print(f"  Gemini Close IDs ({len(gc_ids)}): {sorted(gc_ids)}")
        
        # Check overlaps
        gr_overlap = len(gt_ids.intersection(gr_ids))
        gc_overlap = len(gt_ids.intersection(gc_ids))
        
        print(f"  Reasoning overlap with GT: {gr_overlap}/{len(gt_ids)} ({gr_overlap/len(gt_ids)*100:.1f}%)")
        print(f"  Close overlap with GT: {gc_overlap}/{len(gt_ids)} ({gc_overlap/len(gt_ids)*100:.1f}%)")
        
        # Missing IDs
        gr_missing = gt_ids - gr_ids
        gc_missing = gt_ids - gc_ids
        
        if gr_missing:
            print(f"  Reasoning missing: {sorted(gr_missing)}")
        if gc_missing:
            print(f"  Close missing: {sorted(gc_missing)}")
        
        print()
    
    # Recommendations
    print("RECOMMENDATIONS:")
    print("-" * 40)
    
    if reasoning_valid > close_valid:
        print("✓ Gemini with reasoning (500 tokens) performs better than close reasoning")
    elif close_valid > reasoning_valid:
        print("✓ Gemini close reasoning performs better than reasoning (500 tokens)")
    else:
        print("⚠ Both Gemini variants show similar performance")
    
    if gr_avg > gc_avg:
        print("✓ Reasoning mode generates more knowledge items on average")
    else:
        print("⚠ Close mode generates more knowledge items on average")
    
    if gr_time_avg > gc_time_avg:
        print("⚠ Reasoning mode is slower than close mode")
    else:
        print("✓ Reasoning mode is faster than close mode")
    
    print()
    print("=" * 80)

if __name__ == "__main__":
    analyze_results()
