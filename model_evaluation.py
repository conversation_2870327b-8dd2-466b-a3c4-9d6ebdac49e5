#!/usr/bin/env python3
"""
Model Evaluation Script: Gemini-2.5 Flash Reasoning vs Close Reasoning
Evaluates model performance against Claude ground truth for knowledge item selection.
"""

import json
import csv
import time
import re
import requests
import uuid
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ModelResult:
    """Store model evaluation results"""
    knowledge_ids: Set[str]
    response_time: float
    raw_response: str

@dataclass
class CaseEvaluation:
    """Store evaluation results for a single case"""
    case_index: int
    case_content: str
    ground_truth_ids: Set[str]
    ground_truth_count_range: Tuple[int, int]
    ground_truth_time_range: Tuple[float, float]

    gemini_reasoning_ids: Set[str]
    gemini_reasoning_valid: bool
    gemini_reasoning_count_range: Tuple[int, int]
    gemini_reasoning_time_range: Tuple[float, float]

    gemini_close_ids: Set[str]
    gemini_close_valid: bool
    gemini_close_count_range: Tuple[int, int]
    gemini_close_time_range: Tuple[float, float]

class ModelEvaluator:
    """Main evaluation class"""

    def __init__(self):
        self.gemini_ak = "L0m34Ncl68uymsVan1Pk9VrIbqW1VXuE_GPT_AK"
        self.claude_ak = "QlqvAeHFYSuqzoQSH3kuxzl2N5anzDUb_GPT_AK"
        self.gemini_url = "https://search.bytedance.net/gpt/openapi/online/multimodal/crawl"
        self.claude_url = "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"

    def generate_logid(self) -> str:
        """Generate unique log ID"""
        return str(uuid.uuid4())

    def extract_knowledge_ids(self, response: str) -> Set[str]:
        """Extract knowledge IDs from model response"""
        pattern = r'<id>([^<]+)</id>'
        matches = re.findall(pattern, response, re.IGNORECASE)
        return set(matches)

    def call_claude(self, messages: List[Dict]) -> ModelResult:
        """Call Claude model"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        # Convert messages format for Claude
        claude_messages = []
        for msg in messages:
            if msg['role'] == 'system':
                # Claude doesn't have system role, prepend to user message
                continue
            elif msg['role'] == 'user':
                # Combine system and user content
                system_content = ""
                for m in messages:
                    if m['role'] == 'system':
                        system_content = m['content'] + "\n\n"
                        break

                claude_messages.append({
                    "role": "user",
                    "content": [{
                        "type": "text",
                        "text": system_content + msg['content']
                    }]
                })

        payload = {
            "stream": False,
            "messages": claude_messages,
            "model": "aws_sdk_claude37_sonnet",
            "max_tokens": 1024,
            "Temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.claude_url}?ak={self.claude_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Claude API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gemini_reasoning(self, messages: List[Dict]) -> ModelResult:
        """Call Gemini with reasoning (500 tokens)"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        payload = {
            "stream": False,
            "model": "gemini-2.5-flash",
            "max_tokens": 2048,
            "messages": messages,
            "thinking": {
                "include_thoughts": True,
                "budget_tokens": 500
            },
            "temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.gemini_url}?ak={self.gemini_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Gemini reasoning API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gemini_close(self, messages: List[Dict]) -> ModelResult:
        """Call Gemini without reasoning"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        payload = {
            "stream": False,
            "model": "gemini-2.5-flash",
            "max_tokens": 2048,
            "messages": messages,
            "thinking": {
                "include_thoughts": False,
                "budget_tokens": 0
            },
            "temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.gemini_url}?ak={self.gemini_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Gemini close API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_model_multiple_times(self, model_func, messages: List[Dict], num_calls: int = 3) -> Tuple[Set[str], Tuple[int, int], Tuple[float, float], List[ModelResult]]:
        """Call a model multiple times and return intersection and ranges"""
        results = []

        for i in range(num_calls):
            logger.info(f"Making call {i+1}/{num_calls}")
            result = model_func(messages)
            results.append(result)
            time.sleep(1)  # Rate limiting

        # Calculate intersection of knowledge IDs
        if results:
            intersection = results[0].knowledge_ids
            for result in results[1:]:
                intersection = intersection.intersection(result.knowledge_ids)
        else:
            intersection = set()

        # Calculate count and time ranges
        counts = [len(result.knowledge_ids) for result in results]
        times = [result.response_time for result in results]

        count_range = (min(counts) if counts else 0, max(counts) if counts else 0)
        time_range = (min(times) if times else 0.0, max(times) if times else 0.0)

        return intersection, count_range, time_range, results

    def evaluate_case(self, case_index: int, case_messages: List[Dict]) -> CaseEvaluation:
        """Evaluate a single case"""
        logger.info(f"Evaluating case {case_index + 1}")

        # Extract case content for reporting
        case_content = ""
        for msg in case_messages:
            if msg['role'] == 'user':
                case_content = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
                break

        # 1. Establish ground truth with Claude (3 calls)
        logger.info("Establishing ground truth with Claude...")
        ground_truth_ids, gt_count_range, gt_time_range, claude_results = self.call_model_multiple_times(
            self.call_claude, case_messages, 3
        )

        # 2. Evaluate Gemini with reasoning (3 calls)
        logger.info("Evaluating Gemini with reasoning...")
        gemini_reasoning_ids, gr_count_range, gr_time_range, gemini_reasoning_results = self.call_model_multiple_times(
            self.call_gemini_reasoning, case_messages, 3
        )

        # 3. Evaluate Gemini without reasoning (3 calls)
        logger.info("Evaluating Gemini without reasoning...")
        gemini_close_ids, gc_count_range, gc_time_range, gemini_close_results = self.call_model_multiple_times(
            self.call_gemini_close, case_messages, 3
        )

        # Check validity (ground truth should be subset of model results)
        gemini_reasoning_valid = ground_truth_ids.issubset(gemini_reasoning_ids)
        gemini_close_valid = ground_truth_ids.issubset(gemini_close_ids)

        return CaseEvaluation(
            case_index=case_index,
            case_content=case_content,
            ground_truth_ids=ground_truth_ids,
            ground_truth_count_range=gt_count_range,
            ground_truth_time_range=gt_time_range,
            gemini_reasoning_ids=gemini_reasoning_ids,
            gemini_reasoning_valid=gemini_reasoning_valid,
            gemini_reasoning_count_range=gr_count_range,
            gemini_reasoning_time_range=gr_time_range,
            gemini_close_ids=gemini_close_ids,
            gemini_close_valid=gemini_close_valid,
            gemini_close_count_range=gc_count_range,
            gemini_close_time_range=gc_time_range
        )

    def save_results_to_csv(self, evaluations: List[CaseEvaluation], filename: str = "evaluation_results.csv"):
        """Save evaluation results to CSV file"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'case_index',
                'case_content',
                'ground_truth_ids',
                'ground_truth_count_min',
                'ground_truth_count_max',
                'ground_truth_time_min',
                'ground_truth_time_max',
                'gemini_reasoning_ids',
                'gemini_reasoning_valid',
                'gemini_reasoning_count_min',
                'gemini_reasoning_count_max',
                'gemini_reasoning_time_min',
                'gemini_reasoning_time_max',
                'gemini_close_ids',
                'gemini_close_valid',
                'gemini_close_count_min',
                'gemini_close_count_max',
                'gemini_close_time_min',
                'gemini_close_time_max'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for eval_result in evaluations:
                writer.writerow({
                    'case_index': eval_result.case_index + 1,
                    'case_content': eval_result.case_content,
                    'ground_truth_ids': ';'.join(sorted(eval_result.ground_truth_ids)),
                    'ground_truth_count_min': eval_result.ground_truth_count_range[0],
                    'ground_truth_count_max': eval_result.ground_truth_count_range[1],
                    'ground_truth_time_min': round(eval_result.ground_truth_time_range[0], 3),
                    'ground_truth_time_max': round(eval_result.ground_truth_time_range[1], 3),
                    'gemini_reasoning_ids': ';'.join(sorted(eval_result.gemini_reasoning_ids)),
                    'gemini_reasoning_valid': eval_result.gemini_reasoning_valid,
                    'gemini_reasoning_count_min': eval_result.gemini_reasoning_count_range[0],
                    'gemini_reasoning_count_max': eval_result.gemini_reasoning_count_range[1],
                    'gemini_reasoning_time_min': round(eval_result.gemini_reasoning_time_range[0], 3),
                    'gemini_reasoning_time_max': round(eval_result.gemini_reasoning_time_range[1], 3),
                    'gemini_close_ids': ';'.join(sorted(eval_result.gemini_close_ids)),
                    'gemini_close_valid': eval_result.gemini_close_valid,
                    'gemini_close_count_min': eval_result.gemini_close_count_range[0],
                    'gemini_close_count_max': eval_result.gemini_close_count_range[1],
                    'gemini_close_time_min': round(eval_result.gemini_close_time_range[0], 3),
                    'gemini_close_time_max': round(eval_result.gemini_close_time_range[1], 3)
                })

        logger.info(f"Results saved to {filename}")

    def run_evaluation(self, data_file: str = "data_20s.json"):
        """Run the complete evaluation"""
        logger.info("Starting model evaluation...")

        # Load data
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                cases = json.load(f)
        except FileNotFoundError:
            logger.error(f"Data file {data_file} not found")
            return
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON file: {e}")
            return

        logger.info(f"Loaded {len(cases)} cases from {data_file}")

        evaluations = []

        # Process each case
        for i, case_messages in enumerate(cases):
            try:
                evaluation = self.evaluate_case(i, case_messages)
                evaluations.append(evaluation)

                # Log progress
                logger.info(f"Case {i+1}/{len(cases)} completed")
                logger.info(f"Ground truth: {len(evaluation.ground_truth_ids)} IDs")
                logger.info(f"Gemini reasoning valid: {evaluation.gemini_reasoning_valid}")
                logger.info(f"Gemini close valid: {evaluation.gemini_close_valid}")
                logger.info("-" * 50)

            except Exception as e:
                logger.error(f"Error evaluating case {i+1}: {e}")
                continue

        # Save results
        self.save_results_to_csv(evaluations)

        # Print summary
        self.print_summary(evaluations)

    def print_summary(self, evaluations: List[CaseEvaluation]):
        """Print evaluation summary"""
        total_cases = len(evaluations)
        reasoning_valid = sum(1 for e in evaluations if e.gemini_reasoning_valid)
        close_valid = sum(1 for e in evaluations if e.gemini_close_valid)

        logger.info("=" * 60)
        logger.info("EVALUATION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total cases evaluated: {total_cases}")
        logger.info(f"Gemini reasoning valid cases: {reasoning_valid}/{total_cases} ({reasoning_valid/total_cases*100:.1f}%)")
        logger.info(f"Gemini close valid cases: {close_valid}/{total_cases} ({close_valid/total_cases*100:.1f}%)")

        if evaluations:
            avg_gt_count = sum(len(e.ground_truth_ids) for e in evaluations) / len(evaluations)
            avg_reasoning_count = sum(len(e.gemini_reasoning_ids) for e in evaluations) / len(evaluations)
            avg_close_count = sum(len(e.gemini_close_ids) for e in evaluations) / len(evaluations)

            logger.info(f"Average ground truth knowledge count: {avg_gt_count:.1f}")
            logger.info(f"Average Gemini reasoning knowledge count: {avg_reasoning_count:.1f}")
            logger.info(f"Average Gemini close knowledge count: {avg_close_count:.1f}")

def main():
    """Main function"""
    evaluator = ModelEvaluator()
    evaluator.run_evaluation()

if __name__ == "__main__":
    main()
