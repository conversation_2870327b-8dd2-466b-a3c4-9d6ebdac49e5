# 更新版模型评估脚本

本脚本用于评估Gemini-2.5 Flash推理模式与关闭推理模式的效果，以Claude作为ground truth基准。

## 🆕 新功能特性

### 1. 结果缓存与复用
- **数据结构升级**: 支持新的JSON格式，包含`msg`、`result`和`content`字段
- **智能缓存**: 如果case已有`result`，直接复用，避免重复调用Claude
- **自动更新**: 新生成的结果会自动保存回JSON文件

### 2. 完整内容记录
- **第一次调用内容**: 记录每个模型第一次调用的完整响应内容
- **CSV详细输出**: 包含所有模型的原始响应内容，便于人工review
- **JSON内容保存**: Claude的第一次响应内容保存到JSON的`content`字段

## 📊 数据格式

### 输入JSON格式
```json
[
  {
    "msg": [
      {"content": "system prompt", "role": "system"},
      {"content": "user query", "role": "user"}
    ],
    "result": "id1,id2,id3",  // 可选，如果存在则复用
    "content": "完整的Claude响应内容"  // 可选，第一次调用的完整内容
  }
]
```

### CSV输出字段
- `case_index` - 案例编号
- `case_content` - 案例内容预览
- `ground_truth_ids` - Claude交集结果的知识ID
- `ground_truth_content` - Claude第一次调用的完整内容
- `gemini_reasoning_ids` - Gemini推理模式交集结果
- `gemini_reasoning_content` - Gemini推理模式第一次调用的完整内容
- `gemini_close_ids` - Gemini关闭推理模式交集结果
- `gemini_close_content` - Gemini关闭推理模式第一次调用的完整内容
- 各种计数和时间范围字段

## 🚀 使用方法

### 数据格式转换
如果你有旧格式的数据，可以使用转换脚本：
```bash
python convert_data_format.py
```

### 运行评估
```bash
# 使用新格式数据文件
python run_evaluation.py --data-file data_20s_new.json

# 或直接运行主脚本
python model_evaluation.py
```

### 测试API连接
```bash
python run_evaluation.py --test
```

## 💡 智能缓存机制

1. **首次运行**: 如果case的`result`字段为空，调用Claude生成ground truth
2. **后续运行**: 如果case已有`result`，直接复用，节省API调用
3. **自动保存**: 新生成的结果自动保存回原JSON文件
4. **内容记录**: 第一次调用的完整内容保存到`content`字段

## 📈 评估逻辑

1. **Ground Truth建立**:
   - 如果存在`result`：直接使用，时间成本为0
   - 如果不存在：调用Claude 3次，取交集作为ground truth

2. **Gemini评估**:
   - 推理模式：500 token预算，调用3次取交集
   - 关闭推理：0 token预算，调用3次取交集

3. **验证标准**:
   - 检查ground truth是否为Gemini结果的子集
   - 记录知识数量范围和响应时间范围

## 🔍 结果分析

生成的CSV文件包含完整的模型响应内容，便于：
- 人工review模型输出质量
- 分析模型选择知识的reasoning过程
- 对比不同模型的响应风格和准确性
- 识别模型的一致性和稳定性

## 📁 文件说明

- `model_evaluation.py` - 主评估脚本（已更新）
- `convert_data_format.py` - 数据格式转换工具
- `test_data_with_content.json` - 测试数据示例
- `evaluation_results.csv` - 详细评估结果（包含完整内容）
- `data_20s_new.json` - 新格式的数据文件

## ⚡ 性能优化

- **缓存机制**: 避免重复的Claude API调用
- **批量处理**: 支持多个case的批量评估
- **错误处理**: 健壮的错误处理和重试机制
- **进度跟踪**: 详细的日志输出和进度显示

这个更新版本大大提高了评估效率，同时提供了更丰富的分析数据，便于深入理解模型的表现和行为特征。
