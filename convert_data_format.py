#!/usr/bin/env python3
"""
Convert data format from old to new structure
"""

import json

def convert_data_format(input_file: str = "data_20s.json", output_file: str = "data_20s_new.json"):
    """Convert data from old format to new format"""
    
    # Load old format data
    with open(input_file, 'r', encoding='utf-8') as f:
        old_data = json.load(f)
    
    # Convert to new format
    new_data = []
    for case_messages in old_data:
        new_case = {
            "msg": case_messages,
            "result": ""  # Empty result, will be filled by evaluation script
        }
        new_data.append(new_case)
    
    # Save new format data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(new_data, f, ensure_ascii=False, indent=2)
    
    print(f"Converted {len(old_data)} cases from {input_file} to {output_file}")
    print("New format structure:")
    print("- Each case now has 'msg' (messages) and 'result' (knowledge IDs) fields")
    print("- Result field is initially empty and will be populated by evaluation script")

if __name__ == "__main__":
    convert_data_format()
