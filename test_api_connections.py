#!/usr/bin/env python3
"""
Test script to verify API connections before running full evaluation
"""

import json
import requests
import uuid
from model_evaluation import ModelEvaluator

def test_apis():
    """Test all API connections"""
    evaluator = ModelEvaluator()
    
    # Test messages
    test_messages = [
        {
            "content": "You are a helpful assistant. Select relevant knowledge items.",
            "role": "system"
        },
        {
            "content": "I need help with data visualization using Python. What knowledge items would be useful?",
            "role": "user"
        }
    ]
    
    print("Testing API connections...")
    print("=" * 50)
    
    # Test Claude
    print("1. Testing Claude API...")
    try:
        claude_result = evaluator.call_claude(test_messages)
        print(f"✓ Claude API working")
        print(f"  Response time: {claude_result.response_time:.2f}s")
        print(f"  Knowledge IDs found: {len(claude_result.knowledge_ids)}")
        print(f"  IDs: {claude_result.knowledge_ids}")
        print(f"  Response preview: {claude_result.raw_response[:200]}...")
    except Exception as e:
        print(f"✗ Claude API failed: {e}")
    
    print("\n" + "-" * 50)
    
    # Test Gemini with reasoning
    print("2. Testing Gemini with reasoning...")
    try:
        gemini_reasoning_result = evaluator.call_gemini_reasoning(test_messages)
        print(f"✓ Gemini reasoning API working")
        print(f"  Response time: {gemini_reasoning_result.response_time:.2f}s")
        print(f"  Knowledge IDs found: {len(gemini_reasoning_result.knowledge_ids)}")
        print(f"  IDs: {gemini_reasoning_result.knowledge_ids}")
        print(f"  Response preview: {gemini_reasoning_result.raw_response[:200]}...")
    except Exception as e:
        print(f"✗ Gemini reasoning API failed: {e}")
    
    print("\n" + "-" * 50)
    
    # Test Gemini without reasoning
    print("3. Testing Gemini without reasoning...")
    try:
        gemini_close_result = evaluator.call_gemini_close(test_messages)
        print(f"✓ Gemini close API working")
        print(f"  Response time: {gemini_close_result.response_time:.2f}s")
        print(f"  Knowledge IDs found: {len(gemini_close_result.knowledge_ids)}")
        print(f"  IDs: {gemini_close_result.knowledge_ids}")
        print(f"  Response preview: {gemini_close_result.raw_response[:200]}...")
    except Exception as e:
        print(f"✗ Gemini close API failed: {e}")
    
    print("\n" + "=" * 50)
    print("API connection test completed!")

if __name__ == "__main__":
    test_apis()
