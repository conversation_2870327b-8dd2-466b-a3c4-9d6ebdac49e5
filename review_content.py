#!/usr/bin/env python3
"""
Review script for analyzing model content responses
"""

import csv
import json
from typing import Dict, List

def review_csv_content(csv_file: str = "evaluation_results.csv"):
    """Review content from CSV results"""
    
    print("=" * 80)
    print("MODEL CONTENT REVIEW")
    print("=" * 80)
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            results = list(reader)
    except FileNotFoundError:
        print(f"Results file {csv_file} not found.")
        return
    
    for i, result in enumerate(results):
        case_num = result['case_index']
        print(f"\n{'='*60}")
        print(f"CASE {case_num}")
        print(f"{'='*60}")
        
        print(f"Task: {result['case_content'][:100]}...")
        print()
        
        # Ground Truth (Claude)
        print("🔵 GROUND TRUTH (<PERSON>):")
        print("-" * 40)
        print(f"Knowledge IDs: {result['ground_truth_ids']}")
        print(f"Content Preview:")
        gt_content = result['ground_truth_content']
        print(f"{gt_content[:300]}{'...' if len(gt_content) > 300 else ''}")
        print()
        
        # Gemini Reasoning
        print("🟡 GEMINI REASONING (500 tokens):")
        print("-" * 40)
        print(f"Knowledge IDs: {result['gemini_reasoning_ids']}")
        print(f"Valid: {result['gemini_reasoning_valid']}")
        print(f"Content Preview:")
        gr_content = result['gemini_reasoning_content']
        print(f"{gr_content[:300]}{'...' if len(gr_content) > 300 else ''}")
        print()
        
        # Gemini Close
        print("🔴 GEMINI CLOSE (0 tokens):")
        print("-" * 40)
        print(f"Knowledge IDs: {result['gemini_close_ids']}")
        print(f"Valid: {result['gemini_close_valid']}")
        print(f"Content Preview:")
        gc_content = result['gemini_close_content']
        print(f"{gc_content[:300]}{'...' if len(gc_content) > 300 else ''}")
        print()
        
        # Analysis
        print("📊 ANALYSIS:")
        print("-" * 40)
        
        gt_ids = set(result['ground_truth_ids'].split(';')) if result['ground_truth_ids'] else set()
        gr_ids = set(result['gemini_reasoning_ids'].split(';')) if result['gemini_reasoning_ids'] else set()
        gc_ids = set(result['gemini_close_ids'].split(';')) if result['gemini_close_ids'] else set()
        
        if gt_ids:
            gr_overlap = len(gt_ids.intersection(gr_ids)) / len(gt_ids) * 100
            gc_overlap = len(gt_ids.intersection(gc_ids)) / len(gt_ids) * 100
            
            print(f"Reasoning overlap with GT: {gr_overlap:.1f}%")
            print(f"Close overlap with GT: {gc_overlap:.1f}%")
            
            if gr_ids - gt_ids:
                print(f"Reasoning extra IDs: {gr_ids - gt_ids}")
            if gc_ids - gt_ids:
                print(f"Close extra IDs: {gc_ids - gt_ids}")
            if gt_ids - gr_ids:
                print(f"Reasoning missing IDs: {gt_ids - gr_ids}")
            if gt_ids - gc_ids:
                print(f"Close missing IDs: {gt_ids - gc_ids}")

def review_json_content(json_file: str = "test_data_with_content.json"):
    """Review content from JSON data file"""
    
    print("\n" + "=" * 80)
    print("JSON DATA CONTENT REVIEW")
    print("=" * 80)
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Data file {json_file} not found.")
        return
    
    for i, case in enumerate(data):
        print(f"\n{'='*60}")
        print(f"CASE {i+1}")
        print(f"{'='*60}")
        
        # Extract user content
        user_content = ""
        for msg in case['msg']:
            if msg['role'] == 'user':
                user_content = msg['content']
                break
        
        print(f"User Query: {user_content[:100]}...")
        print()
        
        result = case.get('result', '')
        content = case.get('content', '')
        
        if result:
            print(f"🟢 CACHED RESULT: {result}")
        else:
            print("🔴 NO CACHED RESULT")
        
        if content:
            print(f"📝 CACHED CONTENT:")
            print(f"{content[:400]}{'...' if len(content) > 400 else ''}")
        else:
            print("📝 NO CACHED CONTENT")

def main():
    """Main review function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Review model evaluation content')
    parser.add_argument('--csv', default='evaluation_results.csv',
                       help='CSV results file to review')
    parser.add_argument('--json', default='test_data_with_content.json',
                       help='JSON data file to review')
    parser.add_argument('--csv-only', action='store_true',
                       help='Review CSV content only')
    parser.add_argument('--json-only', action='store_true',
                       help='Review JSON content only')
    
    args = parser.parse_args()
    
    if args.json_only:
        review_json_content(args.json)
    elif args.csv_only:
        review_csv_content(args.csv)
    else:
        review_csv_content(args.csv)
        review_json_content(args.json)

if __name__ == "__main__":
    main()
