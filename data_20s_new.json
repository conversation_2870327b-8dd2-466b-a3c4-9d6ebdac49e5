[{"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego tool principle</title>\n<use_this_knowledge_when>\nwhen working with meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 你需要完成一个电商效能数据分析任务，按照以下严格步骤执行：\n\n**任务背景：**\n- 场景：交付效率\n- 基准时间周期：2025-06-01~2025-06-30\n- 对比时间周期：2025-07-01~2025-07-31  \n- 下钻时间周期：2025-04-01~2025-04-30、2025-05-01~2025-05-31\n- 当前团队：消费者整体\n- 下级团队：独立端、搜索、增长、货架/基础链路、货架/店铺、货架/评价、货架/频道、货架/猜你喜欢、货架/商城首页、直播短视频、联盟作者、营销\n\n**指标组信息：**\n1. 交付周期总结：结果指标（研发三周交付率(%)、研发交付周期(80分位)(天)）；过程指标（需求颗粒度(人日)(产品类)、交付异常比例、超三周交付个数*延期超20%、小颗粒度需求超三周交付占比、3pd内需求平均交付周期、20pd以上需求平均交付周期、20pd以上需求数量）\n2. 交付周期*颗粒度：结果指标（小颗粒度需求超三周交付占比）；过程指标（3pd内需求平均交付周期）\n3. 交付周期*延期：结果指标（超三周交付个数*延期超20%）；过程指标（无）\n4. 优先级*颗粒度：结果指标（大颗粒度非P0需求个数、大颗粒度非P0需求资源投入占比、资源投入（PD））；过程指标（无）\n\n**执行步骤：**\n1. 使用indicator_map工具查询所有指标的英文key\n2. 使用custom_team_map工具查询所有团队的英文key\n3. 使用indicator_query工具查询所有时间范围和团队的指标数据\n4. 按照提供的分析规则进行数据分析\n5. 生成符合格式要求的分析报告\n6. 使用docx_save工具保存最终报告\n\n请严格按照提供的分析规则、格式要求和时间映射规则执行，确保分析结论基于实际数据，针对异常团队给出具体的原因分析和优化建议。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}], "result": "mewtwo.6,mewtwo.7,mewtwo.coder_1,mewtwo.dynamic_33,mewtwo.dynamic_38_chart_with_python,mewtwo.dynamic_39_data_visualization", "content": "I need to analyze which knowledge items would be helpful for this data analysis task involving e-commerce performance metrics.\n\n<rationale>This task involves data visualization with Python for analyzing e-commerce performance metrics across different teams and time periods.</rationale>\n<id>mewtwo.dynamic_38_chart_with_python</id>\n\n<rationale>The task requires creating data visualizations to present performance metrics analysis results.</rationale>\n<id>mewtwo.dynamic_39_data_visualization</id>\n\n<rationale>The task involves working with pandas for data manipulation and analysis of the e-commerce metrics.</rationale>\n<id>mewtwo.6</id>\n\n<rationale>The task may require using plotly for creating interactive visualizations of the performance data.</rationale>\n<id>mewtwo.7</id>\n\n<rationale>The task involves writing a comprehensive analysis report document with specific formatting requirements.</rationale>\n<id>mewtwo.dynamic_33</id>\n\n<rationale>The task requires following coding best practices when implementing the data analysis solution.</rationale>\n<id>mewtwo.coder_1</id>\n\n<rationale>The task involves time period comparisons and processing between different date ranges.</rationale>\n<id>mewtwo.15</id>"}]