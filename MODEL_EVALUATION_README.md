# Model Evaluation Script

This script evaluates the effectiveness of Gemini-2.5 Flash with reasoning vs. close reasoning against <PERSON> as ground truth for knowledge item selection tasks.

## Overview

The evaluation process:

1. **Ground Truth Generation**: Calls <PERSON> 3 times for each case and takes the intersection of knowledge IDs as ground truth
2. **Gemini Reasoning Evaluation**: Calls Gemini-2.5 <PERSON> with reasoning (500 tokens) 3 times and takes intersection
3. **Gemini Close Evaluation**: Calls Gemini-2.5 Flash without reasoning 3 times and takes intersection
4. **Validation**: Checks if ground truth is a subset of each Gemini variant's results
5. **Metrics Collection**: Records knowledge count ranges and response time ranges for each model

## Files

- `model_evaluation.py` - Main evaluation script
- `test_api_connections.py` - API connection testing script
- `run_evaluation.py` - Runner script with command-line options
- `requirements.txt` - Python dependencies
- `data_20s.json` - Input data file with test cases

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Test API Connections

Before running the full evaluation, test that all APIs are working:

```bash
python run_evaluation.py --test
```

### Run Full Evaluation

```bash
python run_evaluation.py
```

Or with custom options:

```bash
python run_evaluation.py --data-file data_20s.json --output my_results.csv
```

### Direct Script Usage

```bash
python model_evaluation.py
```

## Output

The script generates a CSV file with the following columns:

- `case_index` - Case number (1-based)
- `case_content` - Preview of the case content
- `ground_truth_ids` - Knowledge IDs from Claude intersection
- `ground_truth_count_min/max` - Range of knowledge counts from Claude calls
- `ground_truth_time_min/max` - Range of response times from Claude calls
- `gemini_reasoning_ids` - Knowledge IDs from Gemini reasoning intersection
- `gemini_reasoning_valid` - Whether ground truth is subset of reasoning results
- `gemini_reasoning_count_min/max` - Range of knowledge counts from reasoning calls
- `gemini_reasoning_time_min/max` - Range of response times from reasoning calls
- `gemini_close_ids` - Knowledge IDs from Gemini close intersection
- `gemini_close_valid` - Whether ground truth is subset of close results
- `gemini_close_count_min/max` - Range of knowledge counts from close calls
- `gemini_close_time_min/max` - Range of response times from close calls

## Data Format

The input JSON file should contain an array of cases, where each case is an array of messages:

```json
[
    [
        {"content": "system prompt", "role": "system"},
        {"content": "user query", "role": "user"}
    ],
    [
        {"content": "system prompt", "role": "system"},
        {"content": "user query", "role": "user"}
    ]
]
```

## API Configuration

The script uses the following APIs:

- **Claude**: `aws_sdk_claude37_sonnet` model
- **Gemini Reasoning**: `gemini-2.5-flash` with 500 token reasoning budget
- **Gemini Close**: `gemini-2.5-flash` with 0 token reasoning budget

API keys and endpoints are configured in the `ModelEvaluator` class.

## Logging

The script provides detailed logging of the evaluation process, including:
- Progress updates for each case
- API call results
- Validation outcomes
- Summary statistics

## Error Handling

The script includes robust error handling for:
- API failures
- Network timeouts
- JSON parsing errors
- Missing files

Failed API calls are logged but don't stop the evaluation process.
