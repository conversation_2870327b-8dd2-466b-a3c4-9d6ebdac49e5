#!/usr/bin/env python3
"""
Complete evaluation summary and final report
"""

import json
import csv
from datetime import datetime

def generate_summary_report():
    """Generate a comprehensive summary report"""
    
    print("=" * 100)
    print("GEMINI-2.5 FLASH REASONING VS CLOSE REASONING EVALUATION REPORT")
    print("=" * 100)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("EVALUATION METHODOLOGY:")
    print("-" * 50)
    print("1. Ground Truth Establishment:")
    print("   - Call Claude 3.7 Sonnet 3 times per case")
    print("   - Take intersection of knowledge IDs as ground truth")
    print("   - Record knowledge count range [min, max] and response time range")
    print()
    print("2. Gemini Reasoning Evaluation (500 tokens):")
    print("   - Call Gemini-2.5 Flash with reasoning budget 500 tokens, 3 times per case")
    print("   - Take intersection of knowledge IDs")
    print("   - Validate: check if ground truth ⊆ reasoning results")
    print("   - Record knowledge count range [min, max] and response time range")
    print()
    print("3. Gemini Close Evaluation (0 tokens):")
    print("   - Call Gemini-2.5 Flash with reasoning budget 0 tokens, 3 times per case")
    print("   - Take intersection of knowledge IDs")
    print("   - Validate: check if ground truth ⊆ close results")
    print("   - Record knowledge count range [min, max] and response time range")
    print()
    
    print("API CONFIGURATIONS:")
    print("-" * 50)
    print("Claude 3.7 Sonnet:")
    print("  - Model: aws_sdk_claude37_sonnet")
    print("  - Max tokens: 1024")
    print("  - Temperature: 0.2")
    print()
    print("Gemini-2.5 Flash (Reasoning):")
    print("  - Model: gemini-2.5-flash")
    print("  - Max tokens: 2048")
    print("  - Temperature: 0.2")
    print("  - Thinking: include_thoughts=true, budget_tokens=500")
    print()
    print("Gemini-2.5 Flash (Close):")
    print("  - Model: gemini-2.5-flash")
    print("  - Max tokens: 2048")
    print("  - Temperature: 0.2")
    print("  - Thinking: include_thoughts=false, budget_tokens=0")
    print()
    
    # Load and display data info
    try:
        with open('data_20s.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("DATA INFORMATION:")
        print("-" * 50)
        print(f"Total test cases: {len(data)}")
        print("Data structure: Array of message arrays")
        print("Each case contains system and user messages for knowledge selection task")
        print()
        
        # Show first case preview
        if data:
            first_case = data[0]
            user_content = ""
            for msg in first_case:
                if msg['role'] == 'user':
                    user_content = msg['content'][:300] + "..." if len(msg['content']) > 300 else msg['content']
                    break
            
            print("Sample case preview:")
            print(f"  {user_content}")
            print()
    
    except Exception as e:
        print(f"Could not load data file: {e}")
        print()
    
    # Load and display results
    try:
        with open('evaluation_results.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            results = list(reader)
        
        print("EVALUATION RESULTS:")
        print("-" * 50)
        
        total_cases = len(results)
        reasoning_valid = sum(1 for r in results if r['gemini_reasoning_valid'] == 'True')
        close_valid = sum(1 for r in results if r['gemini_close_valid'] == 'True')
        
        print(f"Total cases evaluated: {total_cases}")
        print(f"Gemini Reasoning valid: {reasoning_valid}/{total_cases} ({reasoning_valid/total_cases*100:.1f}%)")
        print(f"Gemini Close valid: {close_valid}/{total_cases} ({close_valid/total_cases*100:.1f}%)")
        print()
        
        # Calculate averages
        if results:
            gt_counts = [len(r['ground_truth_ids'].split(';')) if r['ground_truth_ids'] else 0 for r in results]
            gr_counts = [len(r['gemini_reasoning_ids'].split(';')) if r['gemini_reasoning_ids'] else 0 for r in results]
            gc_counts = [len(r['gemini_close_ids'].split(';')) if r['gemini_close_ids'] else 0 for r in results]
            
            gt_times = [(float(r['ground_truth_time_min']) + float(r['ground_truth_time_max'])) / 2 for r in results]
            gr_times = [(float(r['gemini_reasoning_time_min']) + float(r['gemini_reasoning_time_max'])) / 2 for r in results]
            gc_times = [(float(r['gemini_close_time_min']) + float(r['gemini_close_time_max'])) / 2 for r in results]
            
            print("PERFORMANCE METRICS:")
            print("-" * 30)
            print(f"Average Knowledge Count:")
            print(f"  Ground Truth (Claude): {sum(gt_counts)/len(gt_counts):.1f}")
            print(f"  Gemini Reasoning: {sum(gr_counts)/len(gr_counts):.1f}")
            print(f"  Gemini Close: {sum(gc_counts)/len(gc_counts):.1f}")
            print()
            print(f"Average Response Time:")
            print(f"  Ground Truth (Claude): {sum(gt_times)/len(gt_times):.2f}s")
            print(f"  Gemini Reasoning: {sum(gr_times)/len(gr_times):.2f}s")
            print(f"  Gemini Close: {sum(gc_times)/len(gc_times):.2f}s")
            print()
        
        # Detailed case results
        print("DETAILED RESULTS BY CASE:")
        print("-" * 50)
        
        for result in results:
            case_idx = result['case_index']
            gt_ids = set(result['ground_truth_ids'].split(';')) if result['ground_truth_ids'] else set()
            gr_ids = set(result['gemini_reasoning_ids'].split(';')) if result['gemini_reasoning_ids'] else set()
            gc_ids = set(result['gemini_close_ids'].split(';')) if result['gemini_close_ids'] else set()
            
            print(f"Case {case_idx}:")
            print(f"  Ground Truth: {sorted(gt_ids)}")
            print(f"  Reasoning: {sorted(gr_ids)} (Valid: {result['gemini_reasoning_valid']})")
            print(f"  Close: {sorted(gc_ids)} (Valid: {result['gemini_close_valid']})")
            
            # Calculate overlap percentages
            if gt_ids:
                gr_overlap = len(gt_ids.intersection(gr_ids)) / len(gt_ids) * 100
                gc_overlap = len(gt_ids.intersection(gc_ids)) / len(gt_ids) * 100
                print(f"  Overlap with GT: Reasoning {gr_overlap:.1f}%, Close {gc_overlap:.1f}%")
            print()
    
    except Exception as e:
        print(f"Could not load results file: {e}")
        print()
    
    print("CONCLUSIONS:")
    print("-" * 50)
    print("1. Both Gemini variants failed to achieve 100% validation (ground truth subset)")
    print("2. Reasoning mode (500 tokens) shows better knowledge recall than close mode")
    print("3. Close mode is significantly faster but less comprehensive")
    print("4. Claude establishes more consistent ground truth across multiple calls")
    print("5. Further tuning of reasoning budget or prompting may improve results")
    print()
    
    print("FILES GENERATED:")
    print("-" * 50)
    print("- evaluation_results.csv: Detailed results for each case")
    print("- model_evaluation.py: Main evaluation script")
    print("- test_api_connections.py: API testing script")
    print("- run_evaluation.py: Command-line runner")
    print("- analyze_results.py: Results analysis script")
    print("- evaluation_summary.py: This summary report")
    print()
    
    print("=" * 100)

if __name__ == "__main__":
    generate_summary_report()
